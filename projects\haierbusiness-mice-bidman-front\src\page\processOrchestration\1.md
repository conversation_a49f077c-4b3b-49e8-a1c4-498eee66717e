{"data": [{"key": "DEMAND_SUBMIT", "name": "需求提报", "configs": {"demandSubmitLinkMainConfigDefine": {"type": "RADIO", "value": [{"label": "是", "value": "1"}, {"label": "否", "value": "0"}], "name": "是否开启关联主会议功能", "desc": "机票流程用: 开启后表单页面显示关联订单号功能，关联的信息可带出并自动填写。", "required": true}, "demandSubmitFixedCityValueConfigDefine": {"type": "RADIO", "value": [{"label": "不限制", "value": "0"}, {"label": "仅青岛", "value": "1"}], "name": "是否固定会议城市仅青岛", "desc": "配置需求提报时会议城市是否限制仅青岛", "required": true}, "demandSubmitMinimumMiceStartDateConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "会议最短召开日期(单位: 天)", "desc": "当会议创建时,可选择的从当前日期开始最小天数 包含当天", "required": true}}, "nextNodes": {"demandSubmitNextDemandPreInteractDefine": {"nextNode": "DEMAND_PRE_INTERACT"}, "demandSubmitNextDemandReceiveDefine": {"nextNode": "DEMAND_RECEIVE"}}, "nodeDescription": {"name": "需求提报", "stage": "需求", "description": "用户提报会务需求", "role": "经办人"}, "states": [{"code": 100, "isDefault": true, "metaCatalogEnum": "DEMAND_SUBMIT", "desc": "需求提报"}, {"code": 110, "isDefault": false, "metaCatalogEnum": "DEMAND_SUBMIT", "desc": "需求接收驳回"}, {"code": 120, "isDefault": false, "metaCatalogEnum": "DEMAND_SUBMIT", "desc": "需求审批驳回"}]}, {"key": "DEMAND_RECEIVE", "name": "需求接单", "configs": {"demandReceiveRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "需求接收驳回", "value": "110"}], "name": "需求接收驳回状态", "desc": "配置当需求接收驳回时应该到哪一个状态", "required": false, "reverseConfig": true}}, "nextNodes": {"demandReceiveNextDemandPreInteractDefine": {"nextNode": "DEMAND_PRE_INTERACT"}}, "nodeDescription": {"name": "需求接单", "stage": "需求", "description": "会务负责人将会务订单分配给会务顾问", "role": "会务负责人"}, "states": [{"code": 200, "isDefault": true, "metaCatalogEnum": "DEMAND_RECEIVE", "desc": "需求接单"}]}, {"key": "DEMAND_PRE_INTERACT", "name": "需求事先交互", "configs": {}, "nextNodes": {"demandPreInteractNextDemandConfirmDefine": {"nextNode": "DEMAND_CONFIRM"}}, "nodeDescription": {"name": "需求事先交互", "stage": "需求", "description": "会务顾问对用户提报的需求进行完善与修正", "role": "会务顾问"}, "states": [{"code": 300, "isDefault": true, "metaCatalogEnum": "DEMAND_PRE_INTERACT", "desc": "需求事先交互"}, {"code": 310, "isDefault": false, "metaCatalogEnum": "DEMAND_PRE_INTERACT", "desc": "用户需求确认驳回"}, {"code": 320, "isDefault": false, "metaCatalogEnum": "DEMAND_PRE_INTERACT", "desc": "需求发布取消"}]}, {"key": "DEMAND_CONFIRM", "name": "需求确认", "configs": {"vendorInvoiceEntryRejectConfigDefine": {"type": "RADIO", "value": [{"label": "服务商发票录入", "value": "2300"}], "name": "用户驳回发票", "desc": "线上支付时，配置用户驳回发票时应该到哪一个状态", "required": false, "reverseConfig": true}, "platformInvoiceEntryRejectConfigDefine": {"type": "RADIO", "value": [{"label": "平台收款发票录入", "value": "2200"}], "name": "用户驳回发票配置", "desc": "线下支付时，配置用户驳回发票时应该到哪一个状态", "required": false, "reverseConfig": true}, "demandRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "用户需求确认驳回", "value": "310"}], "name": "需求确认驳回状态配置", "desc": "配置当需求确认驳回时应该到哪一个状态", "required": false, "reverseConfig": true}, "platformRefundReceiptUploadRejectConfigDefine": {"type": "RADIO", "value": [{"label": "上传退款凭证（财务）", "value": "2510"}], "name": "用户退款驳回配置", "desc": "配置用户退款驳回时应该到哪一个状态", "required": false, "reverseConfig": true}}, "nextNodes": {"demandConfirmNextDemandApprovalDefine": {"nextNode": "DEMAND_APPROVAL"}}, "nodeDescription": {"name": "需求确认", "stage": "需求", "description": "用户对会务顾问互动后的需求进行确认", "role": "经办人"}, "states": [{"code": 400, "isDefault": true, "metaCatalogEnum": "DEMAND_CONFIRM", "desc": "需求确认"}]}, {"key": "DEMAND_APPROVAL", "name": "需求审批", "configs": {"demandApprovalRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "需求审批驳回", "value": "120"}], "name": "需求提报审批驳回状态配置", "desc": "配置当需求审批驳回时应该到哪一个状态", "required": false}}, "nextNodes": {"demandApprovalNextDemandPushDefine": {"nextNode": "DEMAND_PUSH"}}, "nodeDescription": {"name": "需求审批", "stage": "需求", "description": "用于审批确认提报的需求", "role": "经办人直线"}, "states": [{"code": 500, "isDefault": true, "metaCatalogEnum": "DEMAND_APPROVAL", "desc": "需求审批"}]}, {"key": "DEMAND_PUSH", "name": "需求发布", "configs": {"demandPushSchemeSubmitUrgentAppointDurationConfigDefine": {"type": "RADIO", "value": [{"label": "是", "value": "1"}, {"label": "否", "value": "0"}], "name": "加急是否可指定提报方案时长", "desc": "开启后覆盖默认值。如果开启，加急会议发布页面会出现可设置提报方案的截止时间文本框。", "required": true}, "demandPushSelectedHotelConfigDefine": {"type": "RADIO", "value": [{"label": "是", "value": "1"}, {"label": "否", "value": "0"}], "name": "是否可直接选择酒店", "desc": "开启后，可直接选择酒店进行发布，无需再申请特殊权限。", "required": true}, "demandPushApproveRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "需求发布审批驳回", "value": "620"}], "name": "需求发布审批状态配置", "desc": "配置当需求发布审批时应该到哪一个状态", "required": false, "reverseConfig": true}, "demandPushSchemeSubmitUrgentDurationConfigDefine": {"type": "TIME_OFFSET_SELECTOR", "value": null, "name": "加急提报方案时长(单位: 天+小时)", "desc": "加急提报方案时长，可指定T天后的n的几点完成", "required": true}, "demandCancelPushStateConfigDefine": {"type": "RADIO", "value": [{"label": "需求发布取消", "value": "320"}], "name": "发布取消状态配置", "desc": "配置当发布取消时应该到哪一个状态", "required": false, "reverseConfig": true}, "demandPushSchemeSubmitAppointDurationConfigDefine": {"type": "RADIO", "value": [{"label": "是", "value": "1"}, {"label": "否", "value": "0"}], "name": "是否可指定提报方案时长", "desc": "开启后覆盖默认值。如果开启，发布页面会出现可设置提报方案的截止时间文本框。", "required": true}, "demandPushSchemeSubmitDefaultDurationConfigDefine": {"type": "TIME_OFFSET_SELECTOR", "value": null, "name": "提报方案的默认时长", "desc": "提报方案默认时长，可指定T+n，可指定T天后的n的几点完成", "required": true}}, "nextNodes": {"demandReleaseNextSchemeSubmitDefine": {"nextNode": "SCHEME_SUBMIT"}, "demandReleaseNextDemandReApprovalDefine": {"nextNode": "DEMAND_RE_APPROVAL"}}, "nodeDescription": {"name": "需求发布", "stage": "需求", "description": "会务顾问将会务需求发布给服务商", "role": "会务顾问"}, "states": [{"code": 600, "isDefault": true, "metaCatalogEnum": "DEMAND_PUSH", "desc": "需求发布"}, {"code": 620, "isDefault": false, "metaCatalogEnum": "DEMAND_PUSH", "desc": "需求发布审批驳回"}, {"code": 630, "isDefault": false, "metaCatalogEnum": "DEMAND_PUSH", "desc": "方案提报撤回重新发布"}]}, {"key": "DEMAND_RE_APPROVAL", "name": "需求发布复核", "configs": {"demandReApprovalRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "需求发布审批驳回", "value": "620"}], "name": "需求发布复核状态配置", "desc": "配置当需求发布复核时应该到哪一个状态", "required": false, "reverseConfig": true}}, "nextNodes": {"demandReApprovalNextSchemeSubmitDefine": {"nextNode": "SCHEME_SUBMIT"}}, "nodeDescription": {"name": "需求发布复核", "stage": "需求", "description": "对发布的需求进行审批", "role": "会务顾问直线"}, "states": [{"code": 700, "isDefault": true, "metaCatalogEnum": "DEMAND_RE_APPROVAL", "desc": "需求发布复核"}]}, {"key": "SCHEME_SUBMIT", "name": "方案提报", "configs": {"schemeSubmitMealLabelConfigDefine": {"type": "RADIO", "value": [{"label": "不可修改", "value": "1"}, {"label": "可以提高", "value": "2"}, {"label": "可以降低", "value": "3"}], "name": "是否控制餐标等其他标准配置", "desc": "餐标单价是否可以控制, 如何控制: 不可修改、可以提高，可以降低", "required": true}, "schemeSubmitAgainPushStateConfigDefine": {"type": "RADIO", "value": [{"label": "方案提报撤回重新发布", "value": "630"}], "name": "方案提报撤回重新发布状态配置", "desc": "配置当方案提报退回重新发布时应该到哪一个状态", "required": false, "reverseConfig": true}, "schemeSubmitLockReleaseHourConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "锁定酒店后提报方案截止时间配置(单位: 小时)", "desc": "服务商锁定酒店后填报当前方案的时长，若在该时长内未提报，锁定酒店被释放", "required": true}, "schemeSubmitInteractSchemeQuantityConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "配置互动提报的方案套数最大值配置(取非自动提报服务商最大值)", "desc": "互动提报的方案达到系统设定的最大值时，系统将于一小时后自动终止方案提报互动。", "required": true}, "schemeSubmitOpenInteractHourConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "方案提报开始时间配置(单位: 小时)", "desc": "配置当进入到方案提报节点时,延迟多少小时可以开始提报", "required": true}}, "nextNodes": {"schemeSubmitNextSchemeConfirmDefine": {"nextNode": "SCHEME_CONFIRM"}, "schemeSubmitNextSchemeApprovalDefine": {"nextNode": "SCHEME_APPROVAL"}}, "nodeDescription": {"name": "方案提报", "stage": "方案", "description": "服务商进行方案提报", "role": "服务商"}, "states": [{"code": 800, "isDefault": true, "metaCatalogEnum": "SCHEME_SUBMIT", "desc": "方案提报"}, {"code": 810, "isDefault": false, "metaCatalogEnum": "SCHEME_SUBMIT", "desc": "酒店锁定释放小时数"}, {"code": 820, "isDefault": false, "metaCatalogEnum": "SCHEME_SUBMIT", "desc": "方案提报开始时间配置"}, {"code": 830, "isDefault": false, "metaCatalogEnum": "SCHEME_SUBMIT", "desc": "各供应商可提报方案数量上限"}]}, {"key": "SCHEME_APPROVAL", "name": "方案审核", "configs": {"schemeApprovalAllExcludeConfigDefine": {"type": "RADIO", "value": [{"label": "方案全部排除", "value": "905"}], "name": "方案审核-方案全部排除逆向节点配置", "desc": "配置当方案全部排除时应该到哪一个状态", "required": false, "reverseConfig": true}, "schemeApprovalAgainPushStateConfigDefine": {"type": "RADIO", "value": [{"label": "方案提报撤回重新发布", "value": "630"}], "name": "方案提报撤回重新发布状态配置", "desc": "配置当方案提报退回重新发布时应该到哪一个状态", "required": false, "reverseConfig": true}, "schemeApprovalProhibitMerchantConfigDefine": {"type": "CHECKBOX", "value": [{"label": "云捷达技术服务有限公司（测试）", "value": "5095"}, {"label": "众淼控股（青岛）股份有限公司", "value": "2637"}, {"label": "国网山东省电力公司青岛供电公司", "value": "4876"}, {"label": "海创汇（青岛）科创服务有限公司", "value": "5015"}, {"label": "0714服务商", "value": "5076"}, {"label": "沪联智服企业管理有限公司（测试）", "value": "5064"}, {"label": "汾阳市贾家庄裕和花园酒店", "value": "4763"}, {"label": "测试服务商0709", "value": "5062"}, {"label": "070903", "value": "5061"}, {"label": "看看怎么个事有限公司", "value": "5060"}, {"label": "070902", "value": "5059"}, {"label": "0709", "value": "5058"}, {"label": "0708服务商1", "value": "5055"}, {"label": "0626服务商", "value": "5034"}, {"label": "长三角智联科技服务有限公司", "value": "5024"}, {"label": "青岛美澜朝夕会务服务有限公司", "value": "4927"}, {"label": "佳佳", "value": "5018"}, {"label": "SJ科技服务有限公司", "value": "5017"}, {"label": "0623服务商1", "value": "5016"}, {"label": "十六", "value": "4998"}, {"label": "天津西瓜旅游有限责任公司", "value": "4991"}, {"label": "天津禧云千链企业管理有限公司", "value": "4315"}, {"label": "初一", "value": "4996"}, {"label": "落魄山", "value": "4989"}, {"label": "落魄山祖师堂", "value": "4988"}, {"label": "青岛某某酒店", "value": "4987"}, {"label": "青岛自动化仪表有限公司", "value": "2"}, {"label": "青岛北洋建筑设计有限公司", "value": "4808"}, {"label": "青岛香格里拉大酒店有限公司", "value": "4798"}, {"label": "企业名称3", "value": "4953"}, {"label": "企业名称2", "value": "4952"}, {"label": "青岛建军有限公司", "value": "4941"}, {"label": "青岛建军发展有限公司服务商", "value": "4940"}, {"label": "某某酒店管理有限公司", "value": "4935"}, {"label": "郝代贤0529演示前测试", "value": "4937"}, {"label": "0528测试", "value": "4934"}, {"label": "郝代贤0825演示前测试服务商（勿动）", "value": "4933"}, {"label": "青岛福兴祥商品配送有限公司", "value": "4891"}, {"label": "验证44", "value": "4932"}, {"label": "验证333", "value": "4931"}, {"label": "验证22", "value": "4929"}, {"label": "青岛天地春茶业有限公司", "value": "4732"}, {"label": "演示服务商1", "value": "4920"}, {"label": "中山市阳光商务酒店有限公司", "value": "4889"}, {"label": "北京市完彬集团有限公司", "value": "4919"}, {"label": "山西上云企业管理有限公司", "value": "4913"}, {"label": "Lee服务商测试数据", "value": "4908"}, {"label": "得力集团有限公司", "value": "4899"}, {"label": "青岛健力源营养配餐管理有限公司", "value": "3186"}, {"label": "中台服务商列表", "value": "4789"}, {"label": "青岛健力源餐饮运营管理有限公司", "value": "4547"}, {"label": "青岛美宜家酒店管理服务有限公司", "value": "4745"}, {"label": "青岛青建控股有限公司", "value": "11"}, {"label": "河南皇甲特卫保安服务集团有限公司", "value": "6"}, {"label": "青岛德润房地产评估咨询有限责任公司", "value": "3"}, {"label": "天津市文光集团有限公司", "value": "5"}, {"label": "青岛健力源餐饮管理有限公司", "value": "4780"}, {"label": "山西长江源酒店管理有限公司", "value": "4787"}], "name": "禁止排除服务商名单", "desc": "配置哪些服务商的方案不可排除", "required": false}}, "nextNodes": {"schemeApprovalNextSchemeReApprovalDefine": {"nextNode": "SCHEME_RE_APPROVAL"}}, "nodeDescription": {"name": "方案审核", "stage": "方案", "description": "审核服务商提报的方案，排除不符合需求的方案", "role": "会务顾问"}, "states": [{"code": 900, "isDefault": true, "metaCatalogEnum": "SCHEME_APPROVAL", "desc": "方案审核"}, {"code": 905, "isDefault": false, "metaCatalogEnum": "SCHEME_APPROVAL", "desc": "方案全部排除"}, {"code": 910, "isDefault": false, "metaCatalogEnum": "SCHEME_APPROVAL", "desc": "方案复审驳回"}]}, {"key": "SCHEME_RE_APPROVAL", "name": "方案复审", "configs": {"schemeApprovalRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "方案复审驳回", "value": "910"}], "name": "方案复审状态配置", "desc": "配置当方案复审驳回时应该到哪一个状态", "required": false, "reverseConfig": true}}, "nextNodes": {"schemeReApprovalNextSchemeConfirmDefine": {"nextNode": "SCHEME_CONFIRM"}}, "nodeDescription": {"name": "方案复审", "stage": "方案", "description": "用于审核会务顾问选择的组合执行方案", "role": "会务顾问直线"}, "states": [{"code": 1000, "isDefault": true, "metaCatalogEnum": "SCHEME_RE_APPROVAL", "desc": "方案复审"}]}, {"key": "SCHEME_CONFIRM", "name": "方案确认", "configs": {"schemeConfirmSelectedMaxNumConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "方案最多选择数量配置(包含)(单位: 个)", "desc": "用户最多选择几个方案", "required": true}, "schemeConfirmLowPricedSelectedNumConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "方案最低价标的数量配置(包含)(单位: 个)", "desc": "方案最低价标的数量", "required": true}, "schemeConfirmAgainPushStateConfigDefine": {"type": "RADIO", "value": [{"label": "方案提报撤回重新发布", "value": "630"}], "name": "方案确认退回重新发布状态配置", "desc": "配置当方案确认退回重新发布时应该到哪一个状态", "required": false, "reverseConfig": true}, "schemeConfirmRandomSelectedNumConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "随机标的方案数量配置(包含)(单位: 个)", "desc": "随机标的方案数量配置", "required": true}, "schemeConfirmSelectedMinimumConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "方案最少选择数量配置(包含)(单位: 个)", "desc": "用户最少选择几个方案", "required": true}, "schemeConfirmSelectedTypeConfigDefine": {"type": "RADIO", "value": [{"label": "排除模式", "value": "0"}, {"label": "选择模式", "value": "1"}], "name": "选择排除模式还是选择模式配置", "desc": "配置方案选择模式。", "required": true}}, "nextNodes": {"schemeConfirmNextBidResultConfirmDefine": {"nextNode": "BID_RESULT_CONFIRM"}, "schemeConfirmNextBidPushDefine": {"nextNode": "BID_PUSH"}}, "nodeDescription": {"name": "方案确认", "stage": "方案", "description": "用户选择最终组合执行方案", "role": "经办人"}, "states": [{"code": 1100, "isDefault": true, "metaCatalogEnum": "SCHEME_CONFIRM", "desc": "方案确认"}]}, {"key": "BID_PUSH", "name": "竞价推送", "configs": {"bidPushEndTimeConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "竞价截止时间(单位: 小时)", "desc": "竞价的结束时间配置", "required": true}, "bidPushMerchantMinimumConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "最少推送的服务商数量", "desc": "配置竞价推送时最少推送多少家服务商", "required": true}, "bidPushMerchantMaxConfigDefine": {"type": "INPUT_NUMBER", "value": null, "name": "最大推送的服务商数量", "desc": "配置竞价推送时最多推送多少家服务商", "required": true}}, "nextNodes": {"bidPushNextBiddingDefine": {"nextNode": "BIDDING"}}, "nodeDescription": {"name": "竞价推送", "stage": "竞价", "description": "会务顾问发布竞价推送至服务商", "role": "会务顾问"}, "states": [{"code": 1200, "isDefault": true, "metaCatalogEnum": "BID_PUSH", "desc": "竞价推送"}]}, {"key": "BIDDING", "name": "竞价中", "configs": {}, "nextNodes": {"biddingNextBidResultConfirmDefine": {"nextNode": "BID_RESULT_CONFIRM"}}, "nodeDescription": {"name": "竞价中", "stage": "竞价", "description": "服务商对标的方案提报竞价", "role": "服务商"}, "states": [{"code": 1300, "isDefault": true, "metaCatalogEnum": "BIDDING", "desc": "竞价中"}]}, {"key": "BID_RESULT_CONFIRM", "name": "费用支付", "configs": {"bidResultConfirmConfigDefine": {"type": "RADIO", "value": [{"label": "线上", "value": "1"}, {"label": "线下", "value": "2"}, {"label": "线上+线下", "value": "1,2"}], "name": "支付类型配置", "desc": "配置费用支付时的支付类型是预算还是线下支付线上上传支付凭证", "required": true}, "bidResultConfirmAgainPushStateConfigDefine": {"type": "RADIO", "value": [{"label": "方案提报撤回重新发布", "value": "630"}], "name": "费用支付退回重新发布状态配置", "desc": "配置当费用支付退回重新发布时应该到哪一个状态", "required": false, "reverseConfig": true}}, "nextNodes": {"bidResultConfirmNextPaymentConfirmDefine": {"nextNode": "PAYMENT_CONFIRM"}, "bidResultConfirmNextCostApprovalDefine": {"nextNode": "COST_APPROVAL"}}, "nodeDescription": {"name": "费用支付", "stage": "竞价", "description": "用户确认最终执行方案以及中标价格", "role": "经办人"}, "states": [{"code": 1400, "isDefault": true, "metaCatalogEnum": "BID_RESULT_CONFIRM", "desc": "费用支付"}, {"code": 1410, "isDefault": false, "metaCatalogEnum": "BID_RESULT_CONFIRM", "desc": "竞价流标"}]}, {"key": "COST_APPROVAL", "name": "费用审批", "configs": {"bidPaymentConfigDefine": {"type": "RADIO", "value": [{"label": "费用支付", "value": "1400"}], "name": "费用支付节点配置", "desc": "配置当支付取消时应该到哪一个状态", "required": false, "reverseConfig": true}}, "nextNodes": {"costApprovalNextMiceExecutionDefine": {"nextNode": "MICE_PENDING"}}, "nodeDescription": {"name": "费用审批", "stage": "竞价", "description": "用于审批用户发起的费用支付申请", "role": "经办人直线"}, "states": [{"code": 1500, "isDefault": true, "metaCatalogEnum": "COST_APPROVAL", "desc": "费用审批"}]}, {"key": "MICE_PENDING", "name": "会议待执行", "configs": {}, "nextNodes": {"micePendingNextMiceCompletedDefine": {"nextNode": "MICE_EXECUTION"}}, "nodeDescription": {"name": "会议待执行", "stage": "执行", "description": "会务服务前准备", "role": "服务商"}, "states": [{"code": 1550, "isDefault": true, "metaCatalogEnum": "MICE_PENDING", "desc": "会议待执行"}]}, {"key": "MICE_EXECUTION", "name": "会议执行中", "configs": {}, "nextNodes": {"miceExecutionNextMiceCompletedDefine": {"nextNode": "MICE_COMPLETED"}}, "nodeDescription": {"name": "会议执行中", "stage": "执行", "description": "组织并执行线下的会务服务", "role": "服务商"}, "states": [{"code": 1600, "isDefault": true, "metaCatalogEnum": "MICE_EXECUTION", "desc": "会议执行中"}]}, {"key": "MICE_COMPLETED", "name": "会议完成", "configs": {}, "nextNodes": {"miceCompletedNextEndDefine": {"nextNode": "END"}, "miceCompletedNextBillConfirmDefine": {"nextNode": "BILL_CONFIRM"}}, "nodeDescription": {"name": "会议完成", "stage": "执行", "description": "会议完成后上传会议执行过程中产生的账单", "role": "服务商"}, "states": [{"code": 1700, "isDefault": true, "metaCatalogEnum": "MICE_COMPLETED", "desc": "会议完成"}, {"code": 1750, "isDefault": false, "metaCatalogEnum": "MICE_COMPLETED", "desc": "账单确认驳回"}]}, {"key": "BILL_CONFIRM", "name": "账单确认", "configs": {"billConfirmRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "账单确认驳回", "value": "1750"}], "name": "账单确认驳回状态配置", "desc": "配置当账单确认驳回时应该到哪一个状态", "required": true, "reverseConfig": true}}, "nextNodes": {"billConfirmNextBillApprovalDefine": {"nextNode": "BILL_APPROVAL"}}, "nodeDescription": {"name": "账单确认", "stage": "执行", "description": "确认核对服务商上传的账单", "role": "会务顾问"}, "states": [{"code": 1800, "isDefault": true, "metaCatalogEnum": "BILL_CONFIRM", "desc": "账单确认"}, {"code": 1850, "isDefault": false, "metaCatalogEnum": "BILL_CONFIRM", "desc": "账单审批驳回"}]}, {"key": "BILL_APPROVAL", "name": "账单审批", "configs": {"billApprovalRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "账单审批驳回", "value": "1850"}], "name": "账单审批驳回状态配置", "desc": "配置当账单审批驳回时应该到哪一个状态", "required": true}}, "nextNodes": {"billApprovalNextBillReApprovalDefine": {"nextNode": "BILL_RE_APPROVAL"}}, "nodeDescription": {"name": "账单审批", "stage": "执行", "description": "用于审批账单结算申请", "role": "会务顾问直线"}, "states": [{"code": 1900, "isDefault": true, "metaCatalogEnum": "BILL_APPROVAL", "desc": "账单审批"}, {"code": 1950, "isDefault": false, "metaCatalogEnum": "BILL_APPROVAL", "desc": "账单复审驳回"}]}, {"key": "BILL_RE_APPROVAL", "name": "账单复审", "configs": {"billReApprovalRejectStateConfigDefine": {"type": "RADIO", "value": [{"label": "账单复审驳回", "value": "1950"}], "name": "账单复审驳回状态配置", "desc": "配置当账单复审驳回时应该到哪一个状态", "required": true}}, "nextNodes": {"billReApprovalNextPlatformInvoiceEntryDefine": {"nextNode": "PLATFORM_INVOICE_ENTRY"}, "billReApprovalNextSettlementPendingDefine": {"nextNode": "SETTLEMENT_PENDING"}}, "nodeDescription": {"name": "账单复审", "stage": "执行", "description": "用户复核审批账单结算申请", "role": "会务顾问直线"}, "states": [{"code": 2000, "isDefault": true, "metaCatalogEnum": "BILL_RE_APPROVAL", "desc": "账单复审"}]}, {"key": "PAYMENT_CONFIRM", "name": "财务收款确认", "configs": {}, "nextNodes": {"paymentConfirmNextMiceExecutionDefine": {"nextNode": "MICE_PENDING"}}, "nodeDescription": {"name": "财务收款确认", "stage": "结算", "description": "财务人员确认收款", "role": "财务人员"}, "states": [{"code": 2100, "isDefault": true, "metaCatalogEnum": "PAYMENT_CONFIRM", "desc": "财务收款确认"}]}, {"key": "PLATFORM_INVOICE_ENTRY", "name": "平台收款发票录入", "configs": {}, "nextNodes": {"platformInvoiceEntryNextInvoiceConfirmDefine": {"nextNode": "INVOICE_CONFIRM"}, "platformInvoiceEntryNextVendorInvoiceEntryDefine": {"nextNode": "VENDOR_INVOICE_ENTRY"}}, "nodeDescription": {"name": "平台收款发票录入", "stage": "结算", "description": "用于上传收款发票", "role": "财务人员"}, "states": [{"code": 2200, "isDefault": true, "metaCatalogEnum": "PLATFORM_INVOICE_ENTRY", "desc": "平台收款发票录入"}]}, {"key": "VENDOR_INVOICE_ENTRY", "name": "服务商发票录入", "configs": {}, "nextNodes": {"vendorInvoiceEntryNextPlatformInvoiceConfirmDefine": {"nextNode": "PLATFORM_INVOICE_CONFIRM"}}, "nodeDescription": {"name": "服务商发票录入", "stage": "结算", "description": "用于上传发票信息", "role": "服务商"}, "states": [{"code": 2300, "isDefault": true, "metaCatalogEnum": "VENDOR_INVOICE_ENTRY", "desc": "服务商发票录入"}]}, {"key": "INVOICE_CONFIRM", "name": "用户发票确认", "configs": {}, "nextNodes": {"invoiceConfirmNextVendorInvoiceEntryDefine": {"nextNode": "VENDOR_INVOICE_ENTRY"}, "invoiceConfirmNextPlatformRefundReceiptUploadDefine": {"nextNode": "PLATFORM_REFUND_RECEIPT_UPLOAD"}}, "nodeDescription": {"name": "用户发票确认", "stage": "结算", "description": "用户确认上传的发票信息", "role": "经办人"}, "states": [{"code": 2400, "isDefault": true, "metaCatalogEnum": "INVOICE_CONFIRM", "desc": "发票确认"}]}, {"key": "PLATFORM_REFUND_RECEIPT_UPLOAD", "name": "平台上传退款凭证", "configs": {}, "nextNodes": {"platformRefundReceiptUploadNextRefundConfirmDefine": {"nextNode": "REFUND_CONFIRM"}}, "nodeDescription": {"name": "平台上传退款凭证", "stage": "结算", "description": "如有退款，上传退款相关凭证", "role": "财务"}, "states": [{"code": 2510, "isDefault": true, "metaCatalogEnum": "PLATFORM_REFUND_RECEIPT_UPLOAD", "desc": "上传退款凭证（财务）"}]}, {"key": "REFUND_CONFIRM", "name": "用户退款确认", "configs": {}, "nextNodes": {"refundConfirmNextVendorInvoiceEntryDefine": {"nextNode": "VENDOR_INVOICE_ENTRY"}}, "nodeDescription": {"name": "用户退款确认", "stage": "结算", "description": "用户确认平台上传的退款凭证", "role": "经办人"}, "states": [{"code": 2410, "isDefault": true, "metaCatalogEnum": "REFUND_CONFIRM", "desc": "退款确认（用户）"}]}, {"key": "PLATFORM_PAY_RECEIPT_UPLOAD", "name": "上传平台支付凭证", "configs": {}, "nextNodes": {"platformReceiptUploadNextPlatformInvoiceConfirmDefine": {"nextNode": "END"}}, "nodeDescription": {"name": "上传平台支付凭证", "stage": "结算", "description": "上传平台支付凭证", "role": "财务"}, "states": [{"code": 2520, "isDefault": true, "metaCatalogEnum": "PLATFORM_PAY_RECEIPT_UPLOAD", "desc": "确认付款金额（平台）"}, {"code": 2530, "isDefault": true, "metaCatalogEnum": "PLATFORM_PAY_RECEIPT_UPLOAD", "desc": "财务上传支付凭证"}]}, {"key": "PLATFORM_INVOICE_CONFIRM", "name": "平台发票确认", "configs": {}, "nextNodes": {"platformInvoiceConfirmNextPlatformPayReceiptUploadDefine": {"nextNode": "PLATFORM_PAY_RECEIPT_UPLOAD"}}, "nodeDescription": {"name": "平台发票确认", "stage": "结算", "description": "用户确认上传的发票信息", "role": "经办人"}, "states": [{"code": 2600, "isDefault": true, "metaCatalogEnum": "PLATFORM_INVOICE_CONFIRM", "desc": "平台发票确认"}]}, {"key": "SETTLEMENT_PENDING", "name": "结算中（已推送）已推送账单/最终节点", "configs": {}, "nextNodes": {"settlementPendingNextSettlementRecordedDefine": {"nextNode": "SETTLEMENT_RECORDED"}}, "nodeDescription": {"name": "结算中（已推送）已推送账单/最终节点", "stage": "结算", "description": "", "role": ""}, "states": [{"code": 2700, "isDefault": true, "metaCatalogEnum": "SETTLEMENT_PENDING", "desc": "结算中（已推送）已推送账单/最终节点"}]}, {"key": "SETTLEMENT_RECORDED", "name": "结算中（已记账）", "configs": {}, "nextNodes": {"settlementRecordedNextEndDefine": {"nextNode": "END"}}, "nodeDescription": {"name": "结算中（已记账）", "stage": "结算", "description": "", "role": ""}, "states": [{"code": 2800, "isDefault": true, "metaCatalogEnum": "SETTLEMENT_RECORDED", "desc": "结算中（已记账）"}]}, {"key": "END", "name": "流程结束", "configs": {}, "nextNodes": {"endNextEndDefine": {"nextNode": null}}, "nodeDescription": {"name": "流程结束", "stage": "结算", "description": "会议全流程结束", "role": ""}, "states": [{"code": 2900, "isDefault": true, "metaCatalogEnum": "END", "desc": "已结算"}, {"code": 2910, "isDefault": false, "metaCatalogEnum": "END", "desc": "已作废"}, {"code": 3000, "isDefault": false, "metaCatalogEnum": "END", "desc": "会议评价完成"}]}], "code": null, "message": null, "success": true}